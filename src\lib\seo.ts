/* eslint-disable @typescript-eslint/no-explicit-any */
import { getSiteConfig } from '@/lib/data'

export interface SEOData {
  title?: string
  description?: string
  keywords?: string[]
  image?: string
  url?: string
  type?: 'website' | 'article' | 'profile'
  publishedTime?: string
  modifiedTime?: string
  author?: string
  section?: string
  tags?: string[]
  noindex?: boolean
  nofollow?: boolean
}

export function generateSEOTags(data: SEOData = {}) {
  const siteConfig = getSiteConfig()
  
  const {
    title,
    description,
    keywords = [],
    image,
    url,
    type = 'website',
    publishedTime,
    modifiedTime,
    author,
    section,
    tags = [],
    noindex = false,
    nofollow = false,
  } = data

  const pageTitle = title 
    ? `${title} | ${siteConfig.site.name}`
    : siteConfig.seo.defaultTitle
  
  const pageDescription = description || siteConfig.seo.defaultDescription
  const pageUrl = url ? `${siteConfig.site.url}${url}` : siteConfig.site.url
  const pageImage = image ? `${siteConfig.site.url}${image}` : `${siteConfig.site.url}/og-image.jpg`
  const allKeywords = [...siteConfig.seo.keywords, ...keywords]

  return {
    title: pageTitle,
    description: pageDescription,
    keywords: allKeywords.join(', '),
    canonical: pageUrl,
    openGraph: {
      title: pageTitle,
      description: pageDescription,
      url: pageUrl,
      image: pageImage,
      type,
      siteName: siteConfig.site.name,
      locale: 'en_US',
      ...(type === 'article' && {
        publishedTime,
        modifiedTime,
        author,
        section,
        tags,
      }),
    },
    twitter: {
      card: 'summary_large_image',
      title: pageTitle,
      description: pageDescription,
      image: pageImage,
      creator: siteConfig.seo.twitterHandle,
    },
    robots: {
      index: !noindex,
      follow: !nofollow,
      'max-image-preview': 'large',
      'max-snippet': -1,
      'max-video-preview': -1,
    },
  }
}

// Predefined SEO configurations for common pages
export const seoConfigs = {
  home: {
    title: 'Professional Makeup Artist in Biratnagar, Nepal',
    description: 'Expert makeup artist serving Biratnagar, Itahari, Dharan, and surrounding areas. Specializing in bridal, party, and traditional makeup with over 5 years of experience.',
    keywords: ['makeup artist Biratnagar', 'bridal makeup Nepal', 'professional makeup artist', 'makeup artist Itahari', 'makeup artist Dharan'],
    url: '/',
  },
  
  about: {
    title: 'About Anjali - Professional Makeup Artist',
    description: 'Learn about Anjali, a professional makeup artist with over 5 years of experience in bridal, party, and traditional makeup in Biratnagar, Nepal.',
    keywords: ['about makeup artist', 'professional makeup artist Nepal', 'makeup artist experience', 'makeup artist biography'],
    url: '/about',
  },
  
  services: {
    title: 'Makeup Services - Bridal, Party & Traditional',
    description: 'Comprehensive makeup services including bridal makeup, party makeup, traditional looks, and special occasion styling in Biratnagar and surrounding areas.',
    keywords: ['makeup services', 'bridal makeup', 'party makeup', 'traditional makeup', 'makeup artist services'],
    url: '/services',
  },
  
  packages: {
    title: 'Makeup Packages & Pricing',
    description: 'Affordable makeup packages for weddings, parties, and special occasions. View our competitive pricing and book your makeup session today.',
    keywords: ['makeup packages', 'makeup pricing', 'wedding packages', 'makeup deals', 'bridal packages'],
    url: '/packages',
  },
  
  portfolio: {
    title: 'Makeup Portfolio & Gallery',
    description: 'Browse our stunning makeup portfolio showcasing bridal transformations, party looks, and traditional makeup artistry. See our work and get inspired.',
    keywords: ['makeup portfolio', 'makeup gallery', 'bridal makeup photos', 'makeup transformations', 'makeup before after'],
    url: '/portfolio',
  },
  
  blog: {
    title: 'Makeup Tips & Beauty Blog',
    description: 'Expert makeup tips, beauty advice, and latest trends from a professional makeup artist. Learn about skincare, makeup techniques, and beauty secrets.',
    keywords: ['makeup tips', 'beauty blog', 'makeup tutorials', 'beauty advice', 'makeup techniques'],
    url: '/blog',
  },
  
  contact: {
    title: 'Contact & Book Appointment',
    description: 'Contact Anjali Makeup Artist to book your makeup session. Serving Biratnagar, Itahari, Dharan, and surrounding areas. Call or WhatsApp for bookings.',
    keywords: ['book makeup artist', 'makeup appointment', 'contact makeup artist', 'makeup booking', 'makeup consultation'],
    url: '/contact',
  },
}

// Generate structured data for different content types
export function generateStructuredData(type: string, data: Record<string, unknown> = {}) {
  const siteConfig = getSiteConfig()

  switch (type) {
    case 'breadcrumb':
      return {
        '@context': 'https://schema.org',
        '@type': 'BreadcrumbList',
        itemListElement: (data.items as Array<{name: string, url: string}>)?.map((item, index: number) => ({
          '@type': 'ListItem',
          position: index + 1,
          name: item.name,
          item: `${siteConfig.site.url}${item.url}`,
        })) || [],
      }

    case 'faq':
      return {
        '@context': 'https://schema.org',
        '@type': 'FAQPage',
        mainEntity: (data.questions as Array<{question: string, answer: string}>)?.map((faq) => ({
          '@type': 'Question',
          name: faq.question,
          acceptedAnswer: {
            '@type': 'Answer',
            text: faq.answer,
          },
        })) || [],
      }

    case 'review':
      return {
        '@context': 'https://schema.org',
        '@type': 'Review',
        itemReviewed: {
          '@type': 'LocalBusiness',
          name: siteConfig.site.name,
        },
        author: {
          '@type': 'Person',
          name: (data as any).author,
        },
        reviewRating: {
          '@type': 'Rating',
          ratingValue: (data as any).rating,
          bestRating: '5',
        },
        reviewBody: (data as any).content,
        datePublished: (data as any).date,
      }

    case 'service':
      return {
        '@context': 'https://schema.org',
        '@type': 'Service',
        name: (data as any).name,
        description: (data as any).description,
        provider: {
          '@type': 'Organization',
          name: siteConfig.site.name,
        },
        areaServed: siteConfig.serviceAreas?.map((area: any) => area.name) || [],
        serviceType: 'Beauty Service',
        category: 'Makeup Artistry',
      }

    default:
      return null
  }
}

// SEO validation utilities
export function validateSEO(data: SEOData) {
  const issues = []

  if (!data.title || data.title.length < 30 || data.title.length > 60) {
    issues.push('Title should be between 30-60 characters')
  }

  if (!data.description || data.description.length < 120 || data.description.length > 160) {
    issues.push('Description should be between 120-160 characters')
  }

  if (!data.keywords || data.keywords.length === 0) {
    issues.push('Keywords are missing')
  }

  if (data.keywords && data.keywords.length > 10) {
    issues.push('Too many keywords (max 10 recommended)')
  }

  return {
    isValid: issues.length === 0,
    issues,
  }
}

// Generate meta tags for Next.js metadata API
export function generateMetadata(seoData: SEOData) {
  const tags = generateSEOTags(seoData)
  
  return {
    title: tags.title,
    description: tags.description,
    keywords: tags.keywords,
    openGraph: tags.openGraph,
    twitter: tags.twitter,
    robots: tags.robots,
    alternates: {
      canonical: tags.canonical,
    },
  }
}
