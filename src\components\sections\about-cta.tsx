'use client'

import Link from 'next/link'
import { ArrowRight, Phone, Calendar, MessageCircle } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Section } from '@/components/ui/section'
import { AnimatedElement } from '@/components/ui/animated-element'
import { getSiteConfig } from '@/lib/data'
import { generateWhatsAppLink } from '@/lib/utils'

export default function AboutCTA() {
  const siteConfig = getSiteConfig()
  const whatsappLink = generateWhatsAppLink(
    siteConfig.contact.whatsapp,
    "Hi! I&apos;d like to book a makeup consultation. Could you please provide more details about your services and availability?"
  )

  return (
    <Section>
      <div className="relative overflow-hidden rounded-3xl bg-gradient-to-br from-rose-gold via-blush-pink to-lavender p-8 md:p-12 text-white">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-10 left-10 w-32 h-32 bg-white rounded-full blur-3xl"></div>
          <div className="absolute bottom-10 right-10 w-40 h-40 bg-white rounded-full blur-3xl"></div>
          <div className="absolute top-1/2 left-1/3 w-24 h-24 bg-white rounded-full blur-2xl"></div>
        </div>

        <div className="relative grid lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <AnimatedElement animation="slideRight" className="space-y-6">
            <h2 className="font-display text-3xl md:text-4xl lg:text-5xl font-bold leading-tight">
              Ready to Look & Feel
              <span className="block">Amazing?</span>
            </h2>
            
            <p className="text-lg text-white/90 leading-relaxed">
              Let&apos;s create the perfect look for your special occasion. Book a consultation 
              today and discover how professional makeup artistry can enhance your natural beauty.
            </p>

            <div className="flex flex-col sm:flex-row gap-4">
              <Button asChild size="lg" variant="secondary" className="group">
                <Link href={whatsappLink} target="_blank" rel="noopener noreferrer">
                  <MessageCircle className="w-5 h-5 mr-2" />
                  WhatsApp Consultation
                  <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                </Link>
              </Button>
              
              <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-rose-gold-dark">
                <Link href="/contact">
                  <Calendar className="w-5 h-5 mr-2" />
                  Book Appointment
                </Link>
              </Button>
            </div>
          </AnimatedElement>

          {/* Quick Contact Cards */}
          <AnimatedElement animation="slideLeft" delay={0.3} className="space-y-4">
            <Card className="bg-white/20 backdrop-blur-sm border-white/30">
              <CardContent className="p-6">
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
                    <Phone className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-white mb-1">Call or WhatsApp</h3>
                    <p className="text-white/80 text-sm">{siteConfig.contact.phone}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white/20 backdrop-blur-sm border-white/30">
              <CardContent className="p-6">
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
                    <Calendar className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-white mb-1">Business Hours</h3>
                    <p className="text-white/80 text-sm">Mon-Sat: 9AM-6PM, Sun: 10AM-4PM</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white/20 backdrop-blur-sm border-white/30">
              <CardContent className="p-6">
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
                    <MessageCircle className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-white mb-1">Quick Response</h3>
                    <p className="text-white/80 text-sm">Usually respond within 2 hours</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </AnimatedElement>
        </div>
      </div>

      {/* Additional Links */}
      <AnimatedElement animation="slideUp" delay={0.6} className="mt-12">
        <div className="grid md:grid-cols-3 gap-6 text-center">
          <Card className="hover:shadow-lg transition-shadow duration-300">
            <CardContent className="p-6">
              <h3 className="font-display text-lg font-semibold text-text-primary mb-2">
                View Our Work
              </h3>
              <p className="text-text-secondary text-sm mb-4">
                Browse our portfolio of stunning makeup transformations
              </p>
              <Button asChild variant="outline" size="sm">
                <Link href="/portfolio">
                  Portfolio
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Link>
              </Button>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow duration-300">
            <CardContent className="p-6">
              <h3 className="font-display text-lg font-semibold text-text-primary mb-2">
                Our Services
              </h3>
              <p className="text-text-secondary text-sm mb-4">
                Explore our comprehensive range of makeup services
              </p>
              <Button asChild variant="outline" size="sm">
                <Link href="/services">
                  Services
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Link>
              </Button>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow duration-300">
            <CardContent className="p-6">
              <h3 className="font-display text-lg font-semibold text-text-primary mb-2">
                Special Packages
              </h3>
              <p className="text-text-secondary text-sm mb-4">
                Save more with our curated service packages
              </p>
              <Button asChild variant="outline" size="sm">
                <Link href="/packages">
                  Packages
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </AnimatedElement>
    </Section>
  )
}
