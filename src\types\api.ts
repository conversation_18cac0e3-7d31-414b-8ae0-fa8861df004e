// API response types for CMS integration

export interface PaginationMeta {
  page: number
  limit: number
  total: number
  pages: number
}

export interface ApiResponse<T> {
  data?: T
  pagination?: PaginationMeta
  message?: string
  error?: string
}

// Blog types
export interface Blog {
  id: string
  title: string
  slug: string
  excerpt?: string
  content: string
  author: string
  status: 'DRAFT' | 'PUBLISHED' | 'ARCHIVED'
  featured: boolean
  image?: string
  readTime?: string
  metaTitle?: string
  metaDescription?: string
  keywords: string[]
  publishedAt?: string
  createdAt: string
  updatedAt: string
  category?: {
    id: string
    name: string
    slug: string
  }
  tags: Array<{
    tag: {
      id: string
      name: string
      slug: string
    }
  }>
}

export interface BlogsResponse {
  blogs: Blog[]
  pagination: PaginationMeta
}

// Service types
export interface Service {
  id: string
  title: string
  slug: string
  description: string
  features: string[]
  duration?: string
  price?: string
  image?: string
  category?: string
  popular: boolean
  status: 'ACTIVE' | 'INACTIVE' | 'ARCHIVED'
  createdAt: string
  updatedAt: string
}

export interface ServicesResponse {
  services: Service[]
  pagination: PaginationMeta
}

// Package types
export interface Package {
  id: string
  name: string
  slug: string
  description: string
  services: string[]
  features: string[]
  price: string
  originalPrice?: string
  duration?: string
  popular: boolean
  image?: string
  category?: string
  status: 'ACTIVE' | 'INACTIVE' | 'ARCHIVED'
  createdAt: string
  updatedAt: string
}

export interface PackagesResponse {
  packages: Package[]
  pagination: PaginationMeta
}

// Testimonial types
export interface Testimonial {
  id: string
  name: string
  email?: string
  message: string
  rating: number
  image?: string
  service?: string
  status: 'PENDING' | 'APPROVED' | 'REJECTED'
  createdAt: string
  updatedAt: string
}

export interface TestimonialsResponse {
  testimonials: Testimonial[]
  pagination: PaginationMeta
}

// Gallery types
export interface GalleryItem {
  id: string
  title: string
  description?: string
  image: string
  category?: string
  tags: string[]
  featured: boolean
  status: 'ACTIVE' | 'INACTIVE' | 'ARCHIVED'
  createdAt: string
  updatedAt: string
}

export interface GalleryResponse {
  gallery: GalleryItem[]
  pagination: PaginationMeta
}

// Settings types
export interface SiteSetting {
  id: string
  key: string
  value: string
  type: 'TEXT' | 'JSON' | 'BOOLEAN' | 'NUMBER' | 'URL' | 'EMAIL'
  description?: string
  createdAt: string
  updatedAt: string
}

export interface SettingsResponse {
  settings: SiteSetting[]
  grouped: Record<string, SiteSetting[]>
}

// Query parameters
export interface BlogQueryParams {
  page?: number
  limit?: number
  search?: string
  status?: string
  category?: string
  featured?: boolean
}

export interface ServiceQueryParams {
  page?: number
  limit?: number
  search?: string
  status?: string
  category?: string
}

export interface PackageQueryParams {
  page?: number
  limit?: number
  search?: string
  status?: string
  category?: string
}

export interface TestimonialQueryParams {
  page?: number
  limit?: number
  search?: string
  status?: string
  service?: string
}

export interface GalleryQueryParams {
  page?: number
  limit?: number
  search?: string
  status?: string
  category?: string
  featured?: boolean
}
