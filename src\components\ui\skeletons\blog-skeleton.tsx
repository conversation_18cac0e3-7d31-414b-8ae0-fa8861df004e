import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'
import { StaggeredContainer, StaggeredItem } from '@/components/ui/animated-element'

export function BlogCardSkeleton() {
  return (
    <Card className="group hover:shadow-xl transition-all duration-300 border-0 bg-white/80 backdrop-blur-sm overflow-hidden">
      <div className="relative aspect-[16/10] overflow-hidden">
        <Skeleton className="w-full h-full" />
        
        {/* Category badge skeleton */}
        <div className="absolute top-4 left-4">
          <Skeleton className="h-6 w-20 rounded-full" />
        </div>
      </div>
      
      <CardHeader className="pb-3">
        <div className="flex items-center gap-2 mb-2">
          <Skeleton className="w-4 h-4 rounded-full" />
          <Skeleton className="h-3 w-16" />
          <Skeleton className="w-1 h-1 rounded-full" />
          <Skeleton className="h-3 w-20" />
        </div>
        
        <Skeleton className="h-6 w-full mb-2" />
        <Skeleton className="h-6 w-3/4" />
        
        <div className="space-y-2 mt-3">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-5/6" />
          <Skeleton className="h-4 w-2/3" />
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Skeleton className="w-8 h-8 rounded-full" />
            <div className="space-y-1">
              <Skeleton className="h-3 w-16" />
              <Skeleton className="h-3 w-12" />
            </div>
          </div>
          
          <Skeleton className="h-9 w-24 rounded-md" />
        </div>
      </CardContent>
    </Card>
  )
}

export function BlogGridSkeleton() {
  return (
    <StaggeredContainer className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
      {Array.from({ length: 6 }).map((_, index) => (
        <StaggeredItem key={index}>
          <BlogCardSkeleton />
        </StaggeredItem>
      ))}
    </StaggeredContainer>
  )
}

export function FeaturedBlogSkeleton() {
  return (
    <div className="grid lg:grid-cols-2 gap-8 items-center">
      {/* Featured image skeleton */}
      <div className="relative aspect-[4/3] overflow-hidden rounded-2xl">
        <Skeleton className="w-full h-full" />
        
        {/* Category badge skeleton */}
        <div className="absolute top-6 left-6">
          <Skeleton className="h-7 w-24 rounded-full" />
        </div>
      </div>
      
      {/* Content skeleton */}
      <div className="space-y-6">
        <div className="flex items-center gap-3">
          <Skeleton className="w-5 h-5 rounded-full" />
          <Skeleton className="h-4 w-20" />
          <Skeleton className="w-1 h-1 rounded-full" />
          <Skeleton className="h-4 w-24" />
        </div>
        
        <div className="space-y-3">
          <Skeleton className="h-8 w-full" />
          <Skeleton className="h-8 w-4/5" />
        </div>
        
        <div className="space-y-2">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-3/4" />
        </div>
        
        <div className="flex items-center gap-4">
          <Skeleton className="w-12 h-12 rounded-full" />
          <div className="space-y-2">
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-3 w-16" />
          </div>
        </div>
        
        <Skeleton className="h-11 w-32 rounded-md" />
      </div>
    </div>
  )
}
