'use client'

import Link from 'next/link'
import { Facebook, Instagram, Phone, Mail, MapPin, Clock } from 'lucide-react'
import { getSiteConfig, getSocialLinks } from '@/lib/data'
import { generateWhatsAppLink } from '@/lib/utils'
import { PiTiktokLogo } from 'react-icons/pi'
import { trackSocialClick, trackEmailClick, trackWhatsAppClick } from '@/lib/analytics'

const quickLinks = [
  { name: 'About', href: '/about' },
  { name: 'Services', href: '/services' },
  { name: 'Packages', href: '/packages' },
  { name: 'Portfolio', href: '/portfolio' },
  { name: 'Blog', href: '/blog' },
  { name: 'Contact', href: '/contact' },
]

const services = [
  { name: 'Bridal Makeup', href: '/services#bridal-makeup' },
  { name: 'Party Makeup', href: '/services#party-makeup' },
  { name: 'Engagement Makeup', href: '/services#engagement-makeup' },
  { name: 'Traditional Makeup', href: '/services#traditional-makeup' },
  { name: 'Photoshoot Makeup', href: '/services#photoshoot-makeup' },
  { name: 'Makeup Lessons', href: '/services#makeup-lessons' },
]

export default function Footer() {
  const siteConfig = getSiteConfig()
  const socialLinks = getSocialLinks()
  const whatsappLink = generateWhatsAppLink(
    siteConfig.contact.whatsapp,
    siteConfig.whatsappMessage
  )

  return (
    <footer className="bg-gradient-to-br from-cream to-soft-gray">
      <div className="mx-auto max-w-7xl px-4 py-12 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <div className="h-8 w-8 rounded-full bg-gradient-to-r from-rose-gold to-blush-pink flex items-center justify-center">
                <span className="text-white font-display font-bold text-lg">A</span>
              </div>
              <span className="font-display text-xl font-semibold text-text-primary">
                {siteConfig.site.name}
              </span>
            </div>
            <p className="text-text-secondary text-sm leading-relaxed">
              {siteConfig.site.description}
            </p>
            <div className="flex space-x-4">
              <Link
                href={socialLinks.facebook}
                target="_blank"
                rel="noopener noreferrer"
                className="text-text-secondary hover:text-rose-gold-dark transition-colors"
                aria-label="Facebook"
                onClick={() => trackSocialClick('facebook', 'footer')}
              >
                <Facebook className="h-5 w-5" />
              </Link>
              <Link
                href={socialLinks.instagram}
                target="_blank"
                rel="noopener noreferrer"
                className="text-text-secondary hover:text-rose-gold-dark transition-colors"
                aria-label="Instagram"
                onClick={() => trackSocialClick('instagram', 'footer')}
              >
                <Instagram className="h-5 w-5" />
              </Link>
              <Link
                href={socialLinks.tiktok}
                target="_blank"
                rel="noopener noreferrer"
                className="text-text-secondary hover:text-rose-gold-dark transition-colors"
                aria-label="Tiktok"
                onClick={() => trackSocialClick('tiktok', 'footer')}
              >
                <PiTiktokLogo className="h-5 w-5" />
              </Link>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="font-display text-lg font-semibold text-text-primary mb-4">
              Quick Links
            </h3>
            <ul className="space-y-2">
              {quickLinks.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-text-secondary hover:text-rose-gold-dark transition-colors text-sm"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Services */}
          <div>
            <h3 className="font-display text-lg font-semibold text-text-primary mb-4">
              Services
            </h3>
            <ul className="space-y-2">
              {services.map((service) => (
                <li key={service.name}>
                  <Link
                    href={service.href}
                    className="text-text-secondary hover:text-rose-gold-dark transition-colors text-sm"
                  >
                    {service.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="font-display text-lg font-semibold text-text-primary mb-4">
              Contact Info
            </h3>
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <MapPin className="h-4 w-4 text-rose-gold-dark mt-1 flex-shrink-0" />
                <div className="text-sm text-text-secondary">
                  <p>{siteConfig.contact.address.street}</p>
                  <p>{siteConfig.contact.address.city}, {siteConfig.contact.address.state}</p>
                  <p>{siteConfig.contact.address.country}</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <Phone className="h-4 w-4 text-rose-gold-dark flex-shrink-0" />
                <Link
                  href={whatsappLink}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-sm text-text-secondary hover:text-rose-gold-dark transition-colors"
                  onClick={() => trackWhatsAppClick('footer', 'contact_info')}
                >
                  {siteConfig.contact.phone}
                </Link>
              </div>

              <div className="flex items-center space-x-3">
                <Mail className="h-4 w-4 text-rose-gold-dark flex-shrink-0" />
                <Link
                  href={`mailto:${siteConfig.contact.email}`}
                  className="text-sm text-text-secondary hover:text-rose-gold-dark transition-colors"
                  onClick={() => trackEmailClick('footer')}
                >
                  {siteConfig.contact.email}
                </Link>
              </div>
              
              <div className="flex items-start space-x-3">
                <Clock className="h-4 w-4 text-rose-gold-dark mt-1 flex-shrink-0" />
                <div className="text-sm text-text-secondary">
                  <p>Sun-Fri: 9:00 AM - 6:00 PM</p>
                  <p>Sat: 10:00 AM - 4:00 PM</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Service Areas */}
        <div className="mt-8 pt-8 border-t border-gray-200">
          <div className="text-center">
            <h4 className="font-display text-lg font-semibold text-text-primary mb-3">
              Service Areas
            </h4>
            <p className="text-text-secondary text-sm">
              Serving clients across{' '}
              {siteConfig.serviceAreas.map((area, index) => (
                <span key={area.name}>
                  {area.name}
                  {index < siteConfig.serviceAreas.length - 1 && ', '}
                </span>
              ))}
            </p>
          </div>
        </div>

        {/* Copyright */}
        <div className="mt-8 pt-8 border-t border-gray-200">
          <div className="text-center space-y-2">
            <p className="text-text-muted text-sm">
              © {new Date().getFullYear()} {siteConfig.site.name}. All rights reserved.
            </p>
            <p className="text-text-muted text-xs">
              Designed and developed with ❤️ by{' '}
              <Link
                href="https://ashishkamat.com.np/"
                target="_blank"
                rel="noopener noreferrer"
                className="text-rose-gold-dark hover:text-rose-gold transition-colors underline underline-offset-2"
              >
                Ashish Kamat
              </Link>
            </p>
          </div>
        </div>
      </div>
    </footer>
  )
}
