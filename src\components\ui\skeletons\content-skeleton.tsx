import { Skeleton } from '@/components/ui/skeleton'
import { Section } from '@/components/ui/section'
import { AnimatedElement } from '@/components/ui/animated-element'

export function SectionHeaderSkeleton() {
  return (
    <div className="text-center max-w-3xl mx-auto mb-16">
      <Skeleton className="h-4 w-24 mx-auto mb-4" />
      <Skeleton className="h-10 w-3/4 mx-auto mb-6" />
      <div className="space-y-2">
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-5/6 mx-auto" />
      </div>
    </div>
  )
}

export function ContentBlockSkeleton() {
  return (
    <div className="space-y-6">
      <div className="space-y-3">
        <Skeleton className="h-8 w-2/3" />
        <div className="space-y-2">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-3/4" />
        </div>
      </div>
      
      <div className="grid md:grid-cols-2 gap-6">
        <div className="space-y-3">
          <Skeleton className="h-6 w-1/2" />
          <div className="space-y-2">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-5/6" />
          </div>
        </div>
        
        <div className="space-y-3">
          <Skeleton className="h-6 w-1/2" />
          <div className="space-y-2">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-4/5" />
          </div>
        </div>
      </div>
    </div>
  )
}

export function FeatureListSkeleton() {
  return (
    <div className="space-y-4">
      {Array.from({ length: 5 }).map((_, index) => (
        <div key={index} className="flex items-center gap-3">
          <Skeleton className="w-5 h-5 rounded-full flex-shrink-0" />
          <Skeleton className="h-4 w-full" />
        </div>
      ))}
    </div>
  )
}

export function StatsGridSkeleton() {
  return (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
      {Array.from({ length: 4 }).map((_, index) => (
        <div key={index} className="text-center space-y-2">
          <Skeleton className="h-12 w-16 mx-auto" />
          <Skeleton className="h-4 w-20 mx-auto" />
        </div>
      ))}
    </div>
  )
}

export function HeroSkeleton() {
  return (
    <Section className="min-h-screen flex items-center">
      <div className="grid lg:grid-cols-2 gap-12 items-center">
        {/* Content skeleton */}
        <div className="space-y-8">
          <div className="space-y-4">
            <Skeleton className="h-4 w-32" />
            <div className="space-y-3">
              <Skeleton className="h-12 w-full" />
              <Skeleton className="h-12 w-4/5" />
            </div>
            <div className="space-y-2">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-5/6" />
            </div>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-4">
            <Skeleton className="h-12 w-40" />
            <Skeleton className="h-12 w-32" />
          </div>
          
          <div className="flex items-center gap-6">
            <div className="flex -space-x-2">
              {Array.from({ length: 4 }).map((_, i) => (
                <Skeleton key={i} className="w-10 h-10 rounded-full border-2 border-white" />
              ))}
            </div>
            <div className="space-y-1">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-3 w-20" />
            </div>
          </div>
        </div>
        
        {/* Image skeleton */}
        <div className="relative">
          <Skeleton className="aspect-[4/5] w-full rounded-2xl" />
          
          {/* Floating elements */}
          <div className="absolute -top-4 -right-4">
            <Skeleton className="w-20 h-20 rounded-2xl" />
          </div>
          <div className="absolute -bottom-4 -left-4">
            <Skeleton className="w-16 h-16 rounded-xl" />
          </div>
        </div>
      </div>
    </Section>
  )
}

export function GenericSectionSkeleton() {
  return (
    <Section>
      <AnimatedElement animation="fadeIn">
        <SectionHeaderSkeleton />
      </AnimatedElement>
      
      <div className="space-y-12">
        <ContentBlockSkeleton />
        <StatsGridSkeleton />
        <FeatureListSkeleton />
      </div>
    </Section>
  )
}
