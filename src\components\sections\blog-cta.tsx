'use client'

import Link from 'next/link'
import { ArrowRight, MessageCircle, BookOpen, Bell } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Section } from '@/components/ui/section'
import { AnimatedElement } from '@/components/ui/animated-element'
import { getSiteConfig } from '@/lib/data'
import { generateWhatsAppLink } from '@/lib/utils'

export default function BlogCTA() {
  const siteConfig = getSiteConfig()
  const whatsappLink = generateWhatsAppLink(
    siteConfig.contact.whatsapp,
    "Hi! I read your blog and would love to learn more about your makeup services. Could you help me with some personalized advice?"
  )

  return (
    <Section background="gradient">
      <div className="text-center space-y-8">
        <AnimatedElement animation="slideUp">
          <h2 className="font-display text-3xl md:text-4xl lg:text-5xl font-bold text-text-primary leading-tight">
            Want Personalized
            <span className="block text-transparent bg-gradient-to-r from-rose-gold to-blush-pink bg-clip-text">
              Beauty Advice?
            </span>
          </h2>
          
          <p className="text-xl text-text-secondary leading-relaxed max-w-2xl mx-auto">
            Get expert makeup tips tailored to your unique features and style. 
            Book a consultation or follow us for more beauty insights and tutorials.
          </p>
        </AnimatedElement>

        {/* CTA Buttons */}
        <AnimatedElement animation="slideUp" delay={0.3}>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild size="lg" variant="gradient" className="group">
              <Link href={whatsappLink} target="_blank" rel="noopener noreferrer">
                <MessageCircle className="w-5 h-5 mr-2" />
                Get Personal Advice
                <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
              </Link>
            </Button>
            
            <Button asChild size="lg" variant="outline">
              <Link href="/services">
                <BookOpen className="w-5 h-5 mr-2" />
                Book Makeup Lesson
              </Link>
            </Button>
          </div>
        </AnimatedElement>

        {/* Blog Features */}
        <AnimatedElement animation="slideUp" delay={0.5}>
          <div className="grid md:grid-cols-3 gap-6 max-w-4xl mx-auto">
            <Card className="bg-white/80 backdrop-blur-sm border-0">
              <CardContent className="p-6 text-center">
                <div className="w-12 h-12 bg-gradient-to-br from-rose-gold to-blush-pink rounded-full flex items-center justify-center mx-auto mb-3">
                  <BookOpen className="w-6 h-6 text-white" />
                </div>
                <h3 className="font-semibold text-text-primary mb-2">Expert Tips</h3>
                <p className="text-text-secondary text-sm">
                  Professional makeup techniques and beauty secrets from 5+ years of experience
                </p>
              </CardContent>
            </Card>

            <Card className="bg-white/80 backdrop-blur-sm border-0">
              <CardContent className="p-6 text-center">
                <div className="w-12 h-12 bg-gradient-to-br from-blush-pink to-lavender rounded-full flex items-center justify-center mx-auto mb-3">
                  <Bell className="w-6 h-6 text-white" />
                </div>
                <h3 className="font-semibold text-text-primary mb-2">Latest Trends</h3>
                <p className="text-text-secondary text-sm">
                  Stay updated with the newest beauty trends and seasonal makeup styles
                </p>
              </CardContent>
            </Card>

            <Card className="bg-white/80 backdrop-blur-sm border-0">
              <CardContent className="p-6 text-center">
                <div className="w-12 h-12 bg-gradient-to-br from-lavender to-rose-gold rounded-full flex items-center justify-center mx-auto mb-3">
                  <MessageCircle className="w-6 h-6 text-white" />
                </div>
                <h3 className="font-semibold text-text-primary mb-2">Personal Guidance</h3>
                <p className="text-text-secondary text-sm">
                  Get personalized advice and answers to your specific beauty questions
                </p>
              </CardContent>
            </Card>
          </div>
        </AnimatedElement>

        {/* Newsletter Signup */}
        <AnimatedElement animation="slideUp" delay={0.7}>
          <Card className="bg-white/60 backdrop-blur-sm border-0 max-w-2xl mx-auto">
            <CardContent className="p-8 text-center">
              <h3 className="font-display text-xl font-semibold text-text-primary mb-4">
                Stay Updated with Beauty Tips
              </h3>
              <p className="text-text-secondary mb-6">
                Follow us on social media for daily beauty tips, tutorials, and behind-the-scenes content.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild variant="outline">
                  <Link href={siteConfig.social.instagram} target="_blank" rel="noopener noreferrer">
                    Follow on Instagram
                  </Link>
                </Button>
                <Button asChild variant="outline">
                  <Link href={siteConfig.social.facebook} target="_blank" rel="noopener noreferrer">
                    Like on Facebook
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </AnimatedElement>

        {/* Additional Links */}
        <AnimatedElement animation="slideUp" delay={0.9}>
          <div className="flex flex-wrap justify-center gap-4 text-sm">
            <Link 
              href="/services" 
              className="text-text-secondary hover:text-rose-gold-dark transition-colors underline underline-offset-4"
            >
              View Our Services
            </Link>
            <span className="text-text-muted">•</span>
            <Link 
              href="/portfolio" 
              className="text-text-secondary hover:text-rose-gold-dark transition-colors underline underline-offset-4"
            >
              See Our Work
            </Link>
            <span className="text-text-muted">•</span>
            <Link 
              href="/about" 
              className="text-text-secondary hover:text-rose-gold-dark transition-colors underline underline-offset-4"
            >
              About Our Artist
            </Link>
          </div>
        </AnimatedElement>
      </div>
    </Section>
  )
}
