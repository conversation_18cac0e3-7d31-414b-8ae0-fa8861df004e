import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { cmsApi } from '@/services/api'
import type {
  BlogQueryParams,
  ServiceQueryParams,
  PackageQueryParams,
  TestimonialQueryParams,
  GalleryQueryParams,
  Testimonial,
} from '@/types/api'

// Query keys for consistent caching
export const queryKeys = {
  blogs: {
    all: ['blogs'] as const,
    lists: () => [...queryKeys.blogs.all, 'list'] as const,
    list: (params: BlogQueryParams) => [...queryKeys.blogs.lists(), params] as const,
    details: () => [...queryKeys.blogs.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.blogs.details(), id] as const,
    slug: (slug: string) => [...queryKeys.blogs.all, 'slug', slug] as const,
    featured: (limit?: number) => [...queryKeys.blogs.all, 'featured', limit] as const,
    recent: (limit?: number) => [...queryKeys.blogs.all, 'recent', limit] as const,
  },
  services: {
    all: ['services'] as const,
    lists: () => [...queryKeys.services.all, 'list'] as const,
    list: (params: ServiceQueryParams) => [...queryKeys.services.lists(), params] as const,
    details: () => [...queryKeys.services.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.services.details(), id] as const,
    slug: (slug: string) => [...queryKeys.services.all, 'slug', slug] as const,
    active: () => [...queryKeys.services.all, 'active'] as const,
    popular: (limit?: number) => [...queryKeys.services.all, 'popular', limit] as const,
    category: (category: string) => [...queryKeys.services.all, 'category', category] as const,
  },
  packages: {
    all: ['packages'] as const,
    lists: () => [...queryKeys.packages.all, 'list'] as const,
    list: (params: PackageQueryParams) => [...queryKeys.packages.lists(), params] as const,
    details: () => [...queryKeys.packages.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.packages.details(), id] as const,
    slug: (slug: string) => [...queryKeys.packages.all, 'slug', slug] as const,
    active: () => [...queryKeys.packages.all, 'active'] as const,
    popular: (limit?: number) => [...queryKeys.packages.all, 'popular', limit] as const,
    featured: () => [...queryKeys.packages.all, 'featured'] as const,
  },
  testimonials: {
    all: ['testimonials'] as const,
    lists: () => [...queryKeys.testimonials.all, 'list'] as const,
    list: (params: TestimonialQueryParams) => [...queryKeys.testimonials.lists(), params] as const,
    details: () => [...queryKeys.testimonials.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.testimonials.details(), id] as const,
    approved: (limit?: number) => [...queryKeys.testimonials.all, 'approved', limit] as const,
    service: (service: string) => [...queryKeys.testimonials.all, 'service', service] as const,
  },
  gallery: {
    all: ['gallery'] as const,
    lists: () => [...queryKeys.gallery.all, 'list'] as const,
    list: (params: GalleryQueryParams) => [...queryKeys.gallery.lists(), params] as const,
    details: () => [...queryKeys.gallery.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.gallery.details(), id] as const,
    active: (limit?: number) => [...queryKeys.gallery.all, 'active', limit] as const,
    featured: (limit?: number) => [...queryKeys.gallery.all, 'featured', limit] as const,
    category: (category: string) => [...queryKeys.gallery.all, 'category', category] as const,
  },
  settings: {
    all: ['settings'] as const,
    detail: (key: string) => [...queryKeys.settings.all, key] as const,
    siteInfo: () => [...queryKeys.settings.all, 'siteInfo'] as const,
    contactInfo: () => [...queryKeys.settings.all, 'contactInfo'] as const,
    socialMedia: () => [...queryKeys.settings.all, 'socialMedia'] as const,
    seoDefaults: () => [...queryKeys.settings.all, 'seoDefaults'] as const,
    businessInfo: () => [...queryKeys.settings.all, 'businessInfo'] as const,
  },
}

// Blog hooks
export const useBlogs = (params: BlogQueryParams = {}) => {
  return useQuery({
    queryKey: queryKeys.blogs.list(params),
    queryFn: () => cmsApi.blogs.getBlogs(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

export const useBlog = (id: string) => {
  return useQuery({
    queryKey: queryKeys.blogs.detail(id),
    queryFn: () => cmsApi.blogs.getBlog(id),
    enabled: !!id,
  })
}

export const useBlogBySlug = (slug: string) => {
  return useQuery({
    queryKey: queryKeys.blogs.slug(slug),
    queryFn: () => cmsApi.blogs.getBlogBySlug(slug),
    enabled: !!slug,
  })
}

export const useFeaturedBlogs = (limit = 3) => {
  return useQuery({
    queryKey: queryKeys.blogs.featured(limit),
    queryFn: () => cmsApi.blogs.getFeaturedBlogs(limit),
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

export const useRecentBlogs = (limit = 5) => {
  return useQuery({
    queryKey: queryKeys.blogs.recent(limit),
    queryFn: () => cmsApi.blogs.getRecentBlogs(limit),
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

// Service hooks
export const useServices = (params: ServiceQueryParams = {}) => {
  return useQuery({
    queryKey: queryKeys.services.list(params),
    queryFn: () => cmsApi.services.getServices(params),
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

export const useService = (id: string) => {
  return useQuery({
    queryKey: queryKeys.services.detail(id),
    queryFn: () => cmsApi.services.getService(id),
    enabled: !!id,
  })
}

export const useServiceBySlug = (slug: string) => {
  return useQuery({
    queryKey: queryKeys.services.slug(slug),
    queryFn: () => cmsApi.services.getServiceBySlug(slug),
    enabled: !!slug,
  })
}

export const useActiveServices = () => {
  return useQuery({
    queryKey: queryKeys.services.active(),
    queryFn: () => cmsApi.services.getActiveServices(),
    staleTime: 15 * 60 * 1000, // 15 minutes
  })
}

export const usePopularServices = (limit = 6) => {
  return useQuery({
    queryKey: queryKeys.services.popular(limit),
    queryFn: () => cmsApi.services.getPopularServices(limit),
    staleTime: 15 * 60 * 1000, // 15 minutes
  })
}

export const useServicesByCategory = (category: string) => {
  return useQuery({
    queryKey: queryKeys.services.category(category),
    queryFn: () => cmsApi.services.getServicesByCategory(category),
    enabled: !!category,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

// Package hooks
export const usePackages = (params: PackageQueryParams = {}) => {
  return useQuery({
    queryKey: queryKeys.packages.list(params),
    queryFn: () => cmsApi.packages.getPackages(params),
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

export const usePackage = (id: string) => {
  return useQuery({
    queryKey: queryKeys.packages.detail(id),
    queryFn: () => cmsApi.packages.getPackage(id),
    enabled: !!id,
  })
}

export const usePackageBySlug = (slug: string) => {
  return useQuery({
    queryKey: queryKeys.packages.slug(slug),
    queryFn: () => cmsApi.packages.getPackageBySlug(slug),
    enabled: !!slug,
  })
}

export const useActivePackages = () => {
  return useQuery({
    queryKey: queryKeys.packages.active(),
    queryFn: () => cmsApi.packages.getActivePackages(),
    staleTime: 15 * 60 * 1000, // 15 minutes
  })
}

export const usePopularPackages = (limit = 3) => {
  return useQuery({
    queryKey: queryKeys.packages.popular(limit),
    queryFn: () => cmsApi.packages.getPopularPackages(limit),
    staleTime: 15 * 60 * 1000, // 15 minutes
  })
}

export const useFeaturedPackages = () => {
  return useQuery({
    queryKey: queryKeys.packages.featured(),
    queryFn: () => cmsApi.packages.getFeaturedPackages(),
    staleTime: 15 * 60 * 1000, // 15 minutes
  })
}

// Testimonial hooks
export const useTestimonials = (params: TestimonialQueryParams = {}) => {
  return useQuery({
    queryKey: queryKeys.testimonials.list(params),
    queryFn: () => cmsApi.testimonials.getTestimonials(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

export const useTestimonial = (id: string) => {
  return useQuery({
    queryKey: queryKeys.testimonials.detail(id),
    queryFn: () => cmsApi.testimonials.getTestimonial(id),
    enabled: !!id,
  })
}

export const useApprovedTestimonials = (limit?: number) => {
  return useQuery({
    queryKey: queryKeys.testimonials.approved(limit),
    queryFn: () => cmsApi.testimonials.getApprovedTestimonials(limit),
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

export const useTestimonialsByService = (service: string) => {
  return useQuery({
    queryKey: queryKeys.testimonials.service(service),
    queryFn: () => cmsApi.testimonials.getTestimonialsByService(service),
    enabled: !!service,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

// Testimonial mutation
export const useCreateTestimonial = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (data: Omit<Testimonial, 'id' | 'status' | 'createdAt' | 'updatedAt'>) =>
      cmsApi.testimonials.createTestimonial(data),
    onSuccess: () => {
      // Invalidate testimonials queries
      queryClient.invalidateQueries({ queryKey: queryKeys.testimonials.all })
    },
  })
}

// Gallery hooks
export const useGallery = (params: GalleryQueryParams = {}) => {
  return useQuery({
    queryKey: queryKeys.gallery.list(params),
    queryFn: () => cmsApi.gallery.getGallery(params),
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

export const useGalleryItem = (id: string) => {
  return useQuery({
    queryKey: queryKeys.gallery.detail(id),
    queryFn: () => cmsApi.gallery.getGalleryItem(id),
    enabled: !!id,
  })
}

export const useActiveGallery = (limit?: number) => {
  return useQuery({
    queryKey: queryKeys.gallery.active(limit),
    queryFn: () => cmsApi.gallery.getActiveGallery(limit),
    staleTime: 15 * 60 * 1000, // 15 minutes
  })
}

export const useFeaturedGallery = (limit = 12) => {
  return useQuery({
    queryKey: queryKeys.gallery.featured(limit),
    queryFn: () => cmsApi.gallery.getFeaturedGallery(limit),
    staleTime: 15 * 60 * 1000, // 15 minutes
  })
}

export const useGalleryByCategory = (category: string) => {
  return useQuery({
    queryKey: queryKeys.gallery.category(category),
    queryFn: () => cmsApi.gallery.getGalleryByCategory(category),
    enabled: !!category,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

// Settings hooks
export const useSettings = () => {
  return useQuery({
    queryKey: queryKeys.settings.all,
    queryFn: () => cmsApi.settings.getSettings(),
    staleTime: 30 * 60 * 1000, // 30 minutes
  })
}

export const useSetting = (key: string) => {
  return useQuery({
    queryKey: queryKeys.settings.detail(key),
    queryFn: () => cmsApi.settings.getSetting(key),
    enabled: !!key,
    staleTime: 30 * 60 * 1000, // 30 minutes
  })
}

export const useSiteInfo = () => {
  return useQuery({
    queryKey: queryKeys.settings.siteInfo(),
    queryFn: () => cmsApi.settings.getSiteInfo(),
    staleTime: 60 * 60 * 1000, // 1 hour
  })
}

export const useContactInfo = () => {
  return useQuery({
    queryKey: queryKeys.settings.contactInfo(),
    queryFn: () => cmsApi.settings.getContactInfo(),
    staleTime: 60 * 60 * 1000, // 1 hour
  })
}

export const useSocialMedia = () => {
  return useQuery({
    queryKey: queryKeys.settings.socialMedia(),
    queryFn: () => cmsApi.settings.getSocialMedia(),
    staleTime: 60 * 60 * 1000, // 1 hour
  })
}

export const useSEODefaults = () => {
  return useQuery({
    queryKey: queryKeys.settings.seoDefaults(),
    queryFn: () => cmsApi.settings.getSEODefaults(),
    staleTime: 60 * 60 * 1000, // 1 hour
  })
}

export const useBusinessInfo = () => {
  return useQuery({
    queryKey: queryKeys.settings.businessInfo(),
    queryFn: () => cmsApi.settings.getBusinessInfo(),
    staleTime: 60 * 60 * 1000, // 1 hour
  })
}
