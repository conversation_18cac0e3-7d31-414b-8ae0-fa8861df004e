'use client'

import { motion } from 'framer-motion'
import { cn } from '@/lib/utils'

interface AnimatedElementProps {
  children: React.ReactNode
  className?: string
  animation?: 'fadeIn' | 'slideUp' | 'slideLeft' | 'slideRight' | 'scale' | 'bounce'
  delay?: number
  duration?: number
  once?: boolean
}

const animations = {
  fadeIn: {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
  },
  slideUp: {
    initial: { opacity: 0, y: 50 },
    animate: { opacity: 1, y: 0 },
  },
  slideLeft: {
    initial: { opacity: 0, x: 50 },
    animate: { opacity: 1, x: 0 },
  },
  slideRight: {
    initial: { opacity: 0, x: -50 },
    animate: { opacity: 1, x: 0 },
  },
  scale: {
    initial: { opacity: 0, scale: 0.8 },
    animate: { opacity: 1, scale: 1 },
  },
  bounce: {
    initial: { opacity: 0, y: -20 },
    animate: { opacity: 1, y: 0 },
  },
}

export function AnimatedElement({
  children,
  className,
  animation = 'fadeIn',
  delay = 0,
  duration = 0.6,
  once = true,
}: AnimatedElementProps) {
  const selectedAnimation = animations[animation]

  return (
    <motion.div
      className={cn(className)}
      initial={selectedAnimation.initial}
      whileInView={selectedAnimation.animate}
      viewport={{ once, margin: '-100px' }}
      transition={{
        duration,
        delay,
        ease: 'easeOut',
      }}
    >
      {children}
    </motion.div>
  )
}

interface StaggeredContainerProps {
  children: React.ReactNode
  className?: string
  staggerDelay?: number
}

export function StaggeredContainer({
  children,
  className,
  staggerDelay = 0.1,
}: StaggeredContainerProps) {
  return (
    <motion.div
      className={cn(className)}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, margin: '-100px' }}
      variants={{
        hidden: {},
        visible: {
          transition: {
            staggerChildren: staggerDelay,
          },
        },
      }}
    >
      {children}
    </motion.div>
  )
}

interface StaggeredItemProps {
  children: React.ReactNode
  className?: string
  animation?: 'fadeIn' | 'slideUp' | 'slideLeft' | 'slideRight' | 'scale'
}

export function StaggeredItem({
  children,
  className,
  animation = 'slideUp',
}: StaggeredItemProps) {
  const selectedAnimation = animations[animation]

  return (
    <motion.div
      className={cn(className)}
      variants={{
        hidden: selectedAnimation.initial,
        visible: selectedAnimation.animate,
      }}
      transition={{ duration: 0.6, ease: 'easeOut' }}
    >
      {children}
    </motion.div>
  )
}
