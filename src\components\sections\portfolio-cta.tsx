'use client'

import Link from 'next/link'
import { ArrowRight, MessageCircle, Calendar, Camera } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Section } from '@/components/ui/section'
import { AnimatedElement } from '@/components/ui/animated-element'
import { getSiteConfig } from '@/lib/data'
import { generateWhatsAppLink } from '@/lib/utils'

export default function PortfolioCTA() {
  const siteConfig = getSiteConfig()
  const whatsappLink = generateWhatsAppLink(
    siteConfig.contact.whatsapp,
    "Hi! I loved your portfolio and would like to book a makeup session. Could you help me with the details?"
  )

  return (
    <Section background="gradient">
      <div className="text-center space-y-8">
        <AnimatedElement animation="slideUp">
          <h2 className="font-display text-3xl md:text-4xl lg:text-5xl font-bold text-text-primary leading-tight">
            Love What You See?
            <span className="block text-transparent bg-gradient-to-r from-rose-gold to-blush-pink bg-clip-text">
              Let&apos;s Create Magic Together
            </span>
          </h2>
          
          <p className="text-xl text-text-secondary leading-relaxed max-w-2xl mx-auto">
            Ready to be our next stunning transformation? Book your makeup session today 
            and let us help you look and feel absolutely beautiful.
          </p>
        </AnimatedElement>

        {/* CTA Buttons */}
        <AnimatedElement animation="slideUp" delay={0.3}>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild size="lg" variant="gradient" className="group">
              <Link href={whatsappLink} target="_blank" rel="noopener noreferrer">
                <MessageCircle className="w-5 h-5 mr-2" />
                Book Your Session
                <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
              </Link>
            </Button>
            
            <Button asChild size="lg" variant="outline">
              <Link href="/services">
                <Camera className="w-5 h-5 mr-2" />
                View Services
              </Link>
            </Button>
          </div>
        </AnimatedElement>

        {/* Portfolio Features */}
        <AnimatedElement animation="slideUp" delay={0.5}>
          <div className="grid md:grid-cols-3 gap-6 max-w-4xl mx-auto">
            <Card className="bg-white/80 backdrop-blur-sm border-0">
              <CardContent className="p-6 text-center">
                <div className="w-12 h-12 bg-gradient-to-br from-rose-gold to-blush-pink rounded-full flex items-center justify-center mx-auto mb-3">
                  <Camera className="w-6 h-6 text-white" />
                </div>
                <h3 className="font-semibold text-text-primary mb-2">Professional Photography</h3>
                <p className="text-text-secondary text-sm">
                  High-quality photos that showcase your makeup transformation beautifully
                </p>
              </CardContent>
            </Card>

            <Card className="bg-white/80 backdrop-blur-sm border-0">
              <CardContent className="p-6 text-center">
                <div className="w-12 h-12 bg-gradient-to-br from-blush-pink to-lavender rounded-full flex items-center justify-center mx-auto mb-3">
                  <Calendar className="w-6 h-6 text-white" />
                </div>
                <h3 className="font-semibold text-text-primary mb-2">Flexible Scheduling</h3>
                <p className="text-text-secondary text-sm">
                  Book sessions that fit your schedule, including early morning and evening slots
                </p>
              </CardContent>
            </Card>

            <Card className="bg-white/80 backdrop-blur-sm border-0">
              <CardContent className="p-6 text-center">
                <div className="w-12 h-12 bg-gradient-to-br from-lavender to-rose-gold rounded-full flex items-center justify-center mx-auto mb-3">
                  <MessageCircle className="w-6 h-6 text-white" />
                </div>
                <h3 className="font-semibold text-text-primary mb-2">Personal Consultation</h3>
                <p className="text-text-secondary text-sm">
                  Discuss your vision and preferences to create the perfect look for you
                </p>
              </CardContent>
            </Card>
          </div>
        </AnimatedElement>

        {/* Social Proof */}
        <AnimatedElement animation="slideUp" delay={0.7}>
          <Card className="bg-white/60 backdrop-blur-sm border-0 max-w-3xl mx-auto">
            <CardContent className="p-6 text-center">
              <h3 className="font-display text-lg font-semibold text-text-primary mb-4">
                Join Our Happy Clients
              </h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6 text-center">
                <div>
                  <div className="text-2xl font-bold text-text-primary mb-1">100+</div>
                  <div className="text-text-secondary text-sm">Makeup Sessions</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-text-primary mb-1">5.0</div>
                  <div className="text-text-secondary text-sm">Average Rating</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-text-primary mb-1">50+</div>
                  <div className="text-text-secondary text-sm">Happy Clients</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-text-primary mb-1">7</div>
                  <div className="text-text-secondary text-sm">Cities Served</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </AnimatedElement>

        {/* Additional Links */}
        <AnimatedElement animation="slideUp" delay={0.9}>
          <div className="flex flex-wrap justify-center gap-4 text-sm">
            <Link 
              href="/packages" 
              className="text-text-secondary hover:text-rose-gold-dark transition-colors underline underline-offset-4"
            >
              View Packages & Deals
            </Link>
            <span className="text-text-muted">•</span>
            <Link 
              href="/about" 
              className="text-text-secondary hover:text-rose-gold-dark transition-colors underline underline-offset-4"
            >
              About Our Artist
            </Link>
            <span className="text-text-muted">•</span>
            <Link 
              href="/contact" 
              className="text-text-secondary hover:text-rose-gold-dark transition-colors underline underline-offset-4"
            >
              Contact Us
            </Link>
          </div>
        </AnimatedElement>
      </div>
    </Section>
  )
}
