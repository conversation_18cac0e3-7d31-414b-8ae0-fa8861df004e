'use client'

import Link from 'next/link'
import Image from 'next/image'
import { <PERSON><PERSON><PERSON>, Check, Clock, <PERSON>rk<PERSON>, Loader2, AlertCircle } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Section, SectionHeader } from '@/components/ui/section'
import { AnimatedElement, StaggeredContainer, StaggeredItem } from '@/components/ui/animated-element'
import { usePopularPackages, useContactInfo } from '@/hooks/use-api'
import { generateWhatsAppLink } from '@/lib/utils'
import type { Package } from '@/types/api'

export default function PackagesPreview() {
  const { data: packagesData, isLoading, error } = usePopularPackages(2)
  const { data: contactInfo } = useContactInfo()

  const packages = packagesData?.packages || []

  // Loading state
  if (isLoading) {
    return (
      <Section id="packages">
        <AnimatedElement animation="fadeIn">
          <SectionHeader
            subtitle="Special Packages"
            title="Save More with Our Packages"
            description="Curated combinations of our most popular services at special prices. Perfect for brides and special occasions."
          />
        </AnimatedElement>
        <div className="flex justify-center items-center py-20">
          <Loader2 className="w-8 h-8 animate-spin text-rose-gold" />
          <span className="ml-3 text-text-secondary">Loading packages...</span>
        </div>
      </Section>
    )
  }

  // Error state
  if (error) {
    return (
      <Section id="packages">
        <AnimatedElement animation="fadeIn">
          <SectionHeader
            subtitle="Special Packages"
            title="Save More with Our Packages"
            description="Curated combinations of our most popular services at special prices. Perfect for brides and special occasions."
          />
        </AnimatedElement>
        <div className="text-center py-20">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <AlertCircle className="w-8 h-8 text-red-600" />
          </div>
          <h3 className="font-display text-xl font-semibold text-text-primary mb-2">
            Unable to Load Packages
          </h3>
          <p className="text-text-secondary mb-6">
            We&apos;re having trouble loading our packages. Please try again later.
          </p>
          <Button variant="outline" onClick={() => window.location.reload()}>
            Try Again
          </Button>
        </div>
      </Section>
    )
  }

  // No packages state
  if (packages.length === 0) {
    return null
  }

  return (
    <Section id="packages">
      <AnimatedElement animation="fadeIn">
        <SectionHeader
          subtitle="Special Packages"
          title="Complete Beauty Packages"
          description="Save more with our carefully curated packages that combine multiple services for your special occasions."
        />
      </AnimatedElement>

      <StaggeredContainer className="grid md:grid-cols-2 gap-8 mb-12">
        {packages.map((pkg: Package) => (
          <StaggeredItem key={pkg.id}>
            <Card className="group relative overflow-hidden hover:shadow-2xl transition-all duration-300 border-0 bg-white h-full flex flex-col">
              {/* Background Gradient */}
              <div className="absolute inset-0 bg-gradient-to-br from-rose-gold/5 to-blush-pink/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

              {/* Badge */}
              {pkg.popular && (
                <Badge
                  className="absolute top-4 right-4 z-10"
                  variant="default"
                >
                  <Sparkles className="w-3 h-3 mr-1" />
                  Popular
                </Badge>
              )}

              <div className="relative aspect-[16/9] overflow-hidden flex-shrink-0">
                <Image
                  src={pkg.image || `https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=600&h=400&fit=crop&crop=face&q=80`}
                  alt={pkg.name}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-300"
                />
              </div>

              <CardHeader className="relative flex-shrink-0">
                <CardTitle className="text-2xl group-hover:text-rose-gold-dark transition-colors">
                  {pkg.name}
                </CardTitle>
                <CardDescription className="text-text-secondary">
                  {pkg.description}
                </CardDescription>
              </CardHeader>

              <CardContent className="relative flex-1 flex flex-col">
                {/* Pricing */}
                <div className="flex items-center gap-3 mb-4">
                  <div className="text-3xl font-bold text-text-primary">
                    {pkg.price}
                  </div>
                  {pkg.originalPrice && (
                    <>
                      <div className="text-lg text-text-muted line-through">
                        {pkg.originalPrice}
                      </div>
                      <Badge variant="success" className="text-xs">
                        Save Money
                      </Badge>
                    </>
                  )}
                </div>

                {/* Duration */}
                <div className="flex items-center gap-2 text-text-secondary mb-6">
                  <Clock className="w-4 h-4" />
                  <span className="text-sm">{pkg.duration}</span>
                </div>

                {/* Content Area - Flexible */}
                <div className="flex-1 space-y-6">
                  {/* Services Included */}
                  <div className="space-y-3">
                    <h4 className="font-semibold text-text-primary">Includes:</h4>
                    <ul className="space-y-2">
                      {pkg.services.slice(0, 4).map((service: string, idx: number) => (
                        <li key={idx} className="text-sm text-text-secondary flex items-start gap-3">
                          <Check className="w-4 h-4 text-rose-gold-dark mt-0.5 flex-shrink-0" />
                          {service}
                        </li>
                      ))}
                      {pkg.services.length > 4 && (
                        <li className="text-sm text-text-muted italic">
                          +{pkg.services.length - 4} more services...
                        </li>
                      )}
                    </ul>
                  </div>

                  {/* Features List */}
                  {pkg.features.length > 0 && (
                    <div className="space-y-3">
                      <h4 className="font-semibold text-text-primary">Additional Features:</h4>
                      <ul className="space-y-2">
                        {pkg.features.slice(0, 3).map((feature: string, idx: number) => (
                          <li key={idx} className="text-sm text-text-secondary flex items-start gap-2">
                            <Check className="w-4 h-4 text-rose-gold-dark mt-0.5 flex-shrink-0" />
                            <span>{feature}</span>
                          </li>
                        ))}
                        {pkg.features.length > 3 && (
                          <li className="text-sm text-text-muted italic">
                            +{pkg.features.length - 3} more features...
                          </li>
                        )}
                      </ul>
                    </div>
                  )}
                </div>

                {/* CTA Button - Always at bottom */}
                <div className="pt-6 mt-auto">
                  <Button
                    asChild
                    variant="gradient"
                    className="w-full group"
                    size="lg"
                  >
                    <Link
                      href={generateWhatsAppLink(
                        contactInfo?.phone || '',
                        `Hi! I&apos;m interested in the ${pkg.name} package. Could you provide more details?`
                      )}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      Book This Package
                      <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </StaggeredItem>
        ))}
      </StaggeredContainer>

      <AnimatedElement animation="slideUp" delay={0.6} className="text-center">
        <Button asChild variant="outline" size="lg">
          <Link href="/packages">
            View All Packages
            <ArrowRight className="w-5 h-5 ml-2" />
          </Link>
        </Button>
      </AnimatedElement>
    </Section>
  )
}
