import Image from 'next/image'
import { <PERSON>, <PERSON><PERSON><PERSON>, Target } from 'lucide-react'
import { Card, CardContent } from '@/components/ui/card'
import { Section, SectionHeader } from '@/components/ui/section'

const values = [
  {
    icon: Heart,
    title: 'Passion for Beauty',
    description: 'Every makeup session is an opportunity to enhance natural beauty and boost confidence. I believe makeup is an art form that celebrates individuality.'
  },
  {
    icon: Sparkles,
    title: 'Attention to Detail',
    description: 'From the perfect foundation match to the precise eyeliner application, every detail matters in creating a flawless, long-lasting look.'
  },
  {
    icon: Target,
    title: 'Client-Focused Approach',
    description: 'Understanding your vision, preferences, and the occasion helps me create personalized looks that make you feel confident and beautiful.'
  }
]

export default function AboutStory() {
  return (
    <Section background="cream">
      <div className="grid lg:grid-cols-2 gap-16 items-center">
        {/* Story Content */}
        <div className="space-y-8">
          <SectionHeader
            subtitle="My Story"
            title="A Journey of Passion & Artistry"
            description=""
            centered={false}
          />
          
          <div className="space-y-6 text-text-secondary leading-relaxed">
            <p>
              My journey into the world of makeup artistry began over five years ago with a simple 
              fascination for colors, textures, and the transformative power of makeup. What started 
              as a hobby quickly evolved into a passionate career dedicated to helping people look 
              and feel their absolute best.
            </p>
            
            <p>
              Based in the beautiful city of Biratnagar, I&apos;ve had the privilege of working with 
              clients across Nepal, from intimate family gatherings to grand wedding celebrations. 
              Each client brings a unique story, and I consider myself fortunate to be part of 
              their special moments.
            </p>
            
            <p>
              My approach combines traditional techniques with modern trends, ensuring that every 
              look is both timeless and contemporary. Whether it&apos;s a bride&apos;s special day, a 
              professional photoshoot, or a festive celebration, I believe in creating makeup 
              that enhances natural beauty while reflecting personal style.
            </p>
            
            <p>
              Continuous learning is at the heart of my practice. I regularly update my skills 
              with the latest techniques and products, ensuring that my clients receive the best 
              possible service with current trends and high-quality products.
            </p>
          </div>
        </div>

        {/* Image Collage */}
        <div className="relative">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-4">
              <div className="relative aspect-[3/4] rounded-xl overflow-hidden shadow-lg">
                <Image
                  src="https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=300&h=400&fit=crop&crop=face"
                  alt="Makeup artistry work"
                  fill
                  className="object-cover"
                />
              </div>
              <div className="relative aspect-square rounded-xl overflow-hidden shadow-lg">
                <Image
                  src="https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=300&h=300&fit=crop&crop=face"
                  alt="Professional makeup session"
                  fill
                  className="object-cover"
                />
              </div>
            </div>
            <div className="space-y-4 pt-8">
              <div className="relative aspect-square rounded-xl overflow-hidden shadow-lg">
                <Image
                  src="https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=300&h=300&fit=crop&crop=face"
                  alt="Bridal makeup"
                  fill
                  className="object-cover"
                />
              </div>
              <div className="relative aspect-[3/4] rounded-xl overflow-hidden shadow-lg">
                <Image
                  src="https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=300&h=400&fit=crop&crop=face"
                  alt="Traditional makeup"
                  fill
                  className="object-cover"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Values Section */}
      <div className="mt-20">
        <div>
          <SectionHeader
            subtitle="My Values"
            title="What Drives My Artistry"
            description="The principles that guide every makeup session and client interaction."
          />
        </div>

        <div className="grid md:grid-cols-3 gap-8">
          {values.map((value) => (
            <div key={value.title}>
              <Card className="h-full bg-white/80 backdrop-blur-sm border-0 hover:shadow-lg transition-shadow duration-300">
                <CardContent className="p-6 text-center space-y-4">
                  <div className="w-16 h-16 bg-gradient-to-br from-rose-gold to-blush-pink rounded-full flex items-center justify-center mx-auto">
                    <value.icon className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="font-display text-xl font-semibold text-text-primary">
                    {value.title}
                  </h3>
                  <p className="text-text-secondary leading-relaxed">
                    {value.description}
                  </p>
                </CardContent>
              </Card>
            </div>
          ))}
        </div>
      </div>
    </Section>
  )
}
