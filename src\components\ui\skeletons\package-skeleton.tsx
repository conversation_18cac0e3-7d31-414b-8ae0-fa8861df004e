import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'
import { StaggeredContainer, StaggeredItem } from '@/components/ui/animated-element'

export function PackageCardSkeleton() {
  return (
    <Card className="group hover:shadow-xl transition-all duration-300 border-0 bg-white/80 backdrop-blur-sm relative overflow-hidden">
      {/* Badge skeleton */}
      <div className="absolute top-4 right-4 z-10">
        <Skeleton className="h-6 w-20 rounded-full" />
      </div>
      
      <div className="relative aspect-[4/3] overflow-hidden rounded-t-xl">
        <Skeleton className="w-full h-full" />
      </div>
      
      <CardHeader className="pb-3">
        <Skeleton className="h-7 w-3/4 mb-2" />
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-2/3" />
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Services list skeleton */}
        <div className="space-y-2">
          {Array.from({ length: 4 }).map((_, i) => (
            <div key={i} className="flex items-center gap-2">
              <Skeleton className="w-4 h-4 rounded-full" />
              <Skeleton className="h-3 w-32" />
            </div>
          ))}
        </div>
        
        {/* Duration skeleton */}
        <div className="flex items-center gap-2">
          <Skeleton className="w-4 h-4" />
          <Skeleton className="h-4 w-24" />
        </div>
        
        {/* Pricing skeleton */}
        <div className="bg-gradient-to-r from-rose-gold/10 to-blush-pink/10 rounded-xl p-4">
          <div className="flex items-center justify-between mb-2">
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-5 w-24" />
          </div>
          <div className="flex items-center justify-between mb-3">
            <Skeleton className="h-6 w-28" />
            <Skeleton className="h-4 w-16" />
          </div>
          <Skeleton className="h-10 w-full rounded-md" />
        </div>
      </CardContent>
    </Card>
  )
}

export function PackagesGridSkeleton() {
  return (
    <StaggeredContainer className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
      {Array.from({ length: 6 }).map((_, index) => (
        <StaggeredItem key={index}>
          <PackageCardSkeleton />
        </StaggeredItem>
      ))}
    </StaggeredContainer>
  )
}
