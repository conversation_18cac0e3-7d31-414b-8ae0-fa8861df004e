import servicesData from '@/data/services.json'
import packagesData from '@/data/packages.json'
import testimonialsData from '@/data/testimonials.json'
import galleryData from '@/data/gallery.json'
import blogData from '@/data/blog.json'
import siteConfigData from '@/data/site-config.json'
import { formatPrice, formatDate, generateWhatsAppLink } from '@/lib/utils'

export interface Service {
  id: string
  title: string
  description: string
  features: string[]
  duration: string
  price: string
  image: string
  category: string
  popular: boolean
}

export interface Package {
  id: string
  title: string
  description: string
  services: string[]
  duration: string
  originalPrice: string
  discountedPrice: string
  savings: string
  image: string
  popular: boolean
  badge: string | null
}

export interface Testimonial {
  id: string
  name: string
  location: string
  service: string
  rating: number
  text: string
  image: string
  date: string
  featured: boolean
}

export interface GalleryItem {
  id: string
  title: string
  description: string
  image: string
  category: string
  featured: boolean
  tags: string[]
}

export interface BlogPost {
  id: string
  title: string
  slug: string
  excerpt: string
  content: string
  author: string
  publishedAt: string
  updatedAt: string
  featured: boolean
  image: string
  tags: string[]
  category: string
  readTime: string
  seo: {
    metaTitle: string
    metaDescription: string
    keywords: string[]
  }
}

export interface SiteConfig {
  site: {
    name: string
    tagline: string
    description: string
    url: string
    logo: string
    favicon: string
  }
  contact: {
    phone: string
    whatsapp: string
    email: string
    address: {
      street: string
      city: string
      state: string
      country: string
      zipCode: string
    }
    workingHours: Record<string, string>
  }
  social: {
    facebook: string
    instagram: string
    tiktok: string
    youtube: string
  }
  serviceAreas: Array<{
    name: string
    primary: boolean
    travelFee: number
  }>
  whatsappMessage: string
  seo: {
    defaultTitle: string
    defaultDescription: string
    keywords: string[]
    author: string
    twitterHandle: string
  }
  analytics: {
    googleAnalyticsId: string
  }
}

// Services
export function getServices(): Service[] {
  return servicesData.services
}

export function getService(id: string): Service | undefined {
  return servicesData.services.find(service => service.id === id)
}

export function getPopularServices(): Service[] {
  return servicesData.services.filter(service => service.popular)
}

export function getServicesByCategory(category: string): Service[] {
  return servicesData.services.filter(service => service.category === category)
}

// Packages
export function getPackages(): Package[] {
  return packagesData.packages
}

export function getPackage(id: string): Package | undefined {
  return packagesData.packages.find(pkg => pkg.id === id)
}

export function getPopularPackages(): Package[] {
  return packagesData.packages.filter(pkg => pkg.popular)
}

// Testimonials
export function getTestimonials(): Testimonial[] {
  return testimonialsData.testimonials
}

export function getFeaturedTestimonials(): Testimonial[] {
  return testimonialsData.testimonials.filter(testimonial => testimonial.featured)
}

export function getTestimonialsByService(service: string): Testimonial[] {
  return testimonialsData.testimonials.filter(testimonial => 
    testimonial.service.toLowerCase().includes(service.toLowerCase())
  )
}

// Gallery
export function getGalleryItems(): GalleryItem[] {
  return galleryData.gallery
}

export function getFeaturedGalleryItems(): GalleryItem[] {
  return galleryData.gallery.filter(item => item.featured)
}

export function getGalleryItemsByCategory(category: string): GalleryItem[] {
  if (category === 'all') return galleryData.gallery
  return galleryData.gallery.filter(item => item.category === category)
}

export function getGalleryCategories() {
  return galleryData.categories
}

// Blog
export function getBlogPosts(): BlogPost[] {
  return blogData.posts
}

export function getBlogPost(slug: string): BlogPost | undefined {
  return blogData.posts.find(post => post.slug === slug)
}

export function getFeaturedBlogPosts(): BlogPost[] {
  return blogData.posts.filter(post => post.featured)
}

export function getBlogPostsByCategory(category: string): BlogPost[] {
  return blogData.posts.filter(post => 
    post.category.toLowerCase().replace(/\s+/g, '-') === category.toLowerCase()
  )
}

export function getBlogCategories() {
  return blogData.categories
}

export function getRelatedBlogPosts(currentPost: BlogPost, limit: number = 3): BlogPost[] {
  return blogData.posts
    .filter(post => 
      post.id !== currentPost.id && 
      (post.category === currentPost.category || 
       post.tags.some(tag => currentPost.tags.includes(tag)))
    )
    .slice(0, limit)
}

// Site Configuration
export function getSiteConfig(): SiteConfig {
  return {
    ...siteConfigData,
    analytics: {
      googleAnalyticsId: process.env.NEXT_PUBLIC_GA_ID || siteConfigData.analytics.googleAnalyticsId
    }
  } as SiteConfig
}

export function getContactInfo() {
  return siteConfigData.contact
}

export function getSocialLinks() {
  return siteConfigData.social
}

export function getServiceAreas() {
  return siteConfigData.serviceAreas
}

export function getWhatsAppMessage() {
  return siteConfigData.whatsappMessage
}

export function getSEODefaults() {
  return siteConfigData.seo
}

// Re-export utility functions
export { formatPrice, formatDate, generateWhatsAppLink }
