'use client'
/* eslint-disable @typescript-eslint/no-explicit-any */

import { useEffect } from 'react'
import { notFound } from 'next/navigation'
import BlogPostContent from '@/components/sections/blog-post-content'
import { useBlogBySlug } from '@/hooks/use-api'
import { Loader2 } from 'lucide-react'
import { trackBlogPostView } from '@/lib/analytics'

interface BlogPostClientProps {
  slug: string
}

export default function BlogPostClient({ slug }: BlogPostClientProps) {
  const { data: post, isLoading, error } = useBlogBySlug(slug)

  // Track blog post view when post is loaded
  useEffect(() => {
    if (post) {
      trackBlogPostView(post.id, post.title, post.category?.name)
    }
  }, [post])

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin text-rose-gold mx-auto mb-4" />
          <p className="text-text-secondary">Loading article...</p>
        </div>
      </div>
    )
  }

  if (error || !post) {
    notFound()
  }

  return <BlogPostContent post={post as any} />
}
