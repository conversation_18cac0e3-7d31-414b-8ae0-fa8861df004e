'use client'

import Script from 'next/script'
import { getSiteConfig } from '@/lib/data'

export default function Analytics() {
  const siteConfig = getSiteConfig()
  const gaId = siteConfig.analytics.googleAnalyticsId

  // Only load analytics in production or if GA_ID is provided
  if (!gaId || gaId === 'G-XXXXXXXXXX') {
    return null
  }

  return (
    <>
      <Script
        src={`https://www.googletagmanager.com/gtag/js?id=${gaId}`}
        strategy="afterInteractive"
      />
      <Script id="google-analytics" strategy="afterInteractive">
        {`
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}
          gtag('js', new Date());

          gtag('config', '${gaId}', {
            page_title: document.title,
            page_location: window.location.href,
            send_page_view: true,
            // Enhanced ecommerce and engagement tracking
            custom_map: {
              'custom_parameter_1': 'service_type',
              'custom_parameter_2': 'package_type'
            },
            // User engagement tracking
            engagement_time_msec: 100,
            // Conversion tracking
            allow_enhanced_conversions: true,
            // Privacy settings
            anonymize_ip: true,
            allow_google_signals: true,
            allow_ad_personalization_signals: true
          });

          // Track scroll depth
          let scrollDepth = 0;
          window.addEventListener('scroll', function() {
            const scrollPercent = Math.round((window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100);
            if (scrollPercent > scrollDepth && scrollPercent % 25 === 0) {
              scrollDepth = scrollPercent;
              gtag('event', 'scroll_depth', {
                event_category: 'engagement',
                event_label: scrollPercent + '%',
                value: scrollPercent
              });
            }
          });

          // Track time on page
          let startTime = Date.now();
          window.addEventListener('beforeunload', function() {
            const timeOnPage = Math.round((Date.now() - startTime) / 1000);
            gtag('event', 'time_on_page', {
              event_category: 'engagement',
              event_label: 'seconds',
              value: timeOnPage
            });
          });
        `}
      </Script>
    </>
  )
}
