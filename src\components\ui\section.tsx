'use client'

import { cn } from '@/lib/utils'

interface SectionProps {
  children: React.ReactNode
  className?: string
  id?: string
  background?: 'default' | 'cream' | 'soft-gray' | 'gradient'
}

export function Section({ 
  children, 
  className, 
  id, 
  background = 'default' 
}: SectionProps) {
  const backgroundClasses = {
    default: 'bg-white',
    cream: 'bg-cream',
    'soft-gray': 'bg-soft-gray',
    gradient: 'bg-gradient-to-br from-cream to-soft-gray'
  }

  return (
    <section 
      id={id}
      className={cn(
        'py-16 md:py-24',
        backgroundClasses[background],
        className
      )}
    >
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        {children}
      </div>
    </section>
  )
}

interface SectionHeaderProps {
  title: string
  subtitle?: string
  description?: string
  centered?: boolean
  className?: string
}

export function SectionHeader({ 
  title, 
  subtitle, 
  description, 
  centered = true,
  className 
}: SectionHeaderProps) {
  return (
    <div className={cn(
      'mb-12 md:mb-16',
      centered && 'text-center',
      className
    )}>
      {subtitle && (
        <p className="text-rose-gold-dark font-medium text-sm uppercase tracking-wide mb-2">
          {subtitle}
        </p>
      )}
      <h2 className="font-display text-3xl md:text-4xl lg:text-5xl font-bold text-text-primary mb-4">
        {title}
      </h2>
      {description && (
        <p className="text-text-secondary text-lg max-w-3xl mx-auto leading-relaxed">
          {description}
        </p>
      )}
    </div>
  )
}
