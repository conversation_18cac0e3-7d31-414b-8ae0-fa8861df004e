import { Metadata } from 'next'
import { getSiteConfig } from '@/lib/data'

interface PageSEOProps {
  title?: string
  description?: string
  keywords?: string[]
  image?: string
  url?: string
  type?: 'website' | 'article' | 'profile'
  publishedTime?: string
  modifiedTime?: string
  author?: string
  section?: string
  tags?: string[]
  noindex?: boolean
  nofollow?: boolean
}

export function generatePageMetadata({
  title,
  description,
  keywords = [],
  image,
  url,
  type = 'website',
  publishedTime,
  modifiedTime,
  author,
  section,
  tags = [],
  noindex = false,
  nofollow = false,
}: PageSEOProps = {}): Metadata {
  const siteConfig = getSiteConfig()
  
  const pageTitle = title 
    ? `${title} | ${siteConfig.site.name}`
    : siteConfig.seo.defaultTitle
  
  const pageDescription = description || siteConfig.seo.defaultDescription
  const pageUrl = url ? `${siteConfig.site.url}${url}` : siteConfig.site.url
  const pageImage = image ? `${siteConfig.site.url}${image}` : `${siteConfig.site.url}/og-image.jpg`
  const allKeywords = [...siteConfig.seo.keywords, ...keywords]

  const metadata: Metadata = {
    title: pageTitle,
    description: pageDescription,
    keywords: allKeywords,
    authors: author ? [{ name: author }] : [{ name: siteConfig.seo.author }],
    creator: siteConfig.seo.author,
    publisher: siteConfig.site.name,
    
    // Open Graph
    openGraph: {
      type,
      locale: 'en_US',
      url: pageUrl,
      title: pageTitle,
      description: pageDescription,
      siteName: siteConfig.site.name,
      images: [
        {
          url: pageImage,
          width: 1200,
          height: 630,
          alt: title || siteConfig.site.name,
        },
      ],
      ...(type === 'article' && {
        publishedTime,
        modifiedTime,
        authors: author ? [author] : [siteConfig.seo.author],
        section,
        tags,
      }),
    },

    // Twitter
    twitter: {
      card: 'summary_large_image',
      title: pageTitle,
      description: pageDescription,
      creator: siteConfig.seo.twitterHandle,
      images: [pageImage],
    },

    // Robots
    robots: {
      index: !noindex,
      follow: !nofollow,
      googleBot: {
        index: !noindex,
        follow: !nofollow,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },

    // Canonical URL
    alternates: {
      canonical: pageUrl,
    },

    // Additional meta tags
    other: {
      'theme-color': '#f8f9fa',
      'color-scheme': 'light',
      'format-detection': 'telephone=no',
    },
  }

  return metadata
}

// Predefined SEO configurations for common pages
export const pageSEOConfigs = {
  home: {
    title: 'Professional Makeup Artist in Biratnagar, Nepal',
    description: 'Expert makeup artist serving Biratnagar, Itahari, Dharan, and surrounding areas. Specializing in bridal, party, and traditional makeup with over 5 years of experience.',
    keywords: ['makeup artist Biratnagar', 'bridal makeup Nepal', 'professional makeup artist'],
  },
  
  about: {
    title: 'About Anjali - Professional Makeup Artist',
    description: 'Learn about Anjali, a professional makeup artist with over 5 years of experience in bridal, party, and traditional makeup in Biratnagar, Nepal.',
    keywords: ['about makeup artist', 'professional makeup artist Nepal', 'makeup artist experience'],
  },
  
  services: {
    title: 'Makeup Services - Bridal, Party & Traditional',
    description: 'Comprehensive makeup services including bridal makeup, party makeup, traditional looks, and special occasion styling in Biratnagar and surrounding areas.',
    keywords: ['makeup services', 'bridal makeup', 'party makeup', 'traditional makeup'],
  },
  
  packages: {
    title: 'Makeup Packages & Pricing',
    description: 'Affordable makeup packages for weddings, parties, and special occasions. View our competitive pricing and book your makeup session today.',
    keywords: ['makeup packages', 'makeup pricing', 'wedding packages', 'makeup deals'],
  },
  
  portfolio: {
    title: 'Makeup Portfolio & Gallery',
    description: 'Browse our stunning makeup portfolio showcasing bridal transformations, party looks, and traditional makeup artistry. See our work and get inspired.',
    keywords: ['makeup portfolio', 'makeup gallery', 'bridal makeup photos', 'makeup transformations'],
  },
  
  blog: {
    title: 'Makeup Tips & Beauty Blog',
    description: 'Expert makeup tips, beauty advice, and latest trends from a professional makeup artist. Learn about skincare, makeup techniques, and beauty secrets.',
    keywords: ['makeup tips', 'beauty blog', 'makeup tutorials', 'beauty advice'],
  },
  
  contact: {
    title: 'Contact & Book Appointment',
    description: 'Contact Anjali Makeup Artist to book your makeup session. Serving Biratnagar, Itahari, Dharan, and surrounding areas. Call or WhatsApp for bookings.',
    keywords: ['book makeup artist', 'makeup appointment', 'contact makeup artist', 'makeup booking'],
  },
}
